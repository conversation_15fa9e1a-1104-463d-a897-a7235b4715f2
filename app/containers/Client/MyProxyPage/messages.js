import { defineMessages } from 'react-intl';

const scope = 'app.components.common';

export default defineMessages({
  search: {
    id: `${scope}.search`,
    defaultMessage: 'Search',
  },
  allLabel: {
    id: `${scope}.allLabel`,
    defaultMessage: 'All',
  },
  searchPlaceHolder: {
    id: `${scope}.searchPlaceHolder`,
    defaultMessage: 'Code, Name',
  },
  no: {
    id: `${scope}.no`,
    defaultMessage: 'NO',
  },
  status: {
    id: `${scope}.status`,
    defaultMessage: 'Status',
  },
  action: {
    id: `${scope}.action`,
    defaultMessage: 'Action',
  },
  update: {
    id: `${scope}.update`,
    defaultMessage: 'Update',
  },
  create: {
    id: `${scope}.create`,
    defaultMessage: 'Create',
  },
  close: {
    id: `${scope}.close`,
    defaultMessage: 'Close',
  },
  requiredNotNull: {
    id: `${scope}.requiredNotNull`,
    defaultMessage: 'This field is required, please enter!',
  },
  msgUpdateSuccess: {
    id: `${scope}.msgUpdateSuccess`,
    defaultMessage: 'Update Data successful',
  },
  msgUpdateFailed: {
    id: `${scope}.msgUpdateFailed`,
    defaultMessage: 'Update Data failed.',
  },
  msgCreateSuccess: {
    id: `${scope}.msgCreateSuccess`,
    defaultMessage: 'Create Data successful',
  },
  msgCreateFailed: {
    id: `${scope}.msgCreateFailed`,
    defaultMessage: 'Create Data failed',
  },
  msgDeleteSuccess: {
    id: `${scope}.msgDeleteSuccess`,
    defaultMessage: 'Delete Data successful',
  },
  msgDeleteFailed: {
    id: `${scope}.msgDeleteFailed`,
    defaultMessage: 'Delete Data failed',
  },
  notFound: {
    id: `${scope}.notFound`,
    defaultMessage: 'Data not found',
  },
  clearAllFiltersButton: {
    id: `${scope}.clearAllFiltersButton`,
    defaultMessage: 'Clear all fitler',
  },
  advancedSearchButton: {
    id: `${scope}.advancedSearchButton`,
    defaultMessage: 'Advanced search',
  },
  titleConfirm: {
    id: `${scope}.titleConfirm`,
    defaultMessage: 'Confirmation',
  },
  messageConfirm: {
    id: `${scope}.messageConfirm`,
    defaultMessage: 'Do you want to delete this data ?',
  },
  confirmButton: {
    id: `${scope}.confirmButton`,
    defaultMessage: 'Confirm',
  },
  cancelButton: {
    id: `${scope}.cancelButton`,
    defaultMessage: 'Cancel',
  },
  expired_date: {
    id: `${scope}.expired_date`,
    defaultMessage: 'Expired date',
  },
  change_ip: {
    id: `${scope}.change_ip`,
    defaultMessage: 'Change IP',
  },
  authentication: {
    id: `${scope}.authentication`,
    defaultMessage: 'Authentication',
  },
  extend: {
    id: `${scope}.extend`,
    defaultMessage: 'Extend',
  },
  choose_location: {
    id: `${scope}.choose_location`,
    defaultMessage: 'Change Location',
  },
  export_file: {
    id: `${scope}.export_file`,
    defaultMessage: 'Export file',
  },
  confirm_change_ip: {
    id: `${scope}.confirm_change_ip`,
    defaultMessage: 'Are you sure want to change IP?',
  },
  confirm_extend: {
    id: `${scope}.confirm_extend`,
    defaultMessage: 'Are you sure want to extend this license?',
  },
  pkg_license: {
    id: `${scope}.pkg_license`,
    defaultMessage: 'Package / License',
  },
  start_expired_date: {
    id: `${scope}.start_expired_date`,
    defaultMessage: 'Start/Expire Date',
  },
  proxy_detail: {
    id: `${scope}.proxy_detail`,
    defaultMessage: 'Proxy Detail',
  },
  auto_rotation: {
    id: `${scope}.auto_rotation`,
    defaultMessage: 'Auto rotation',
  },
  copy_success: {
    id: `${scope}.copy_success`,
    defaultMessage: 'Copy to clipboard success!',
  },
  tips_password_random: {
    id: `${scope}.tips_password_random`,
    defaultMessage: 'Password will be randomly generated for each port. You can change it during use.',
  },
  password_random: {
    id: `${scope}.password_random`,
    defaultMessage: 'Password will be generated randomly for each port.',
  },
  auto_rotation_title: {
    id: `${scope}.auto_rotation_title`,
    defaultMessage: 'Update auto rotation time',
  },
  auto_rotation_input: {
    id: `${scope}.auto_rotation_input`,
    defaultMessage: 'Auto rotation time (minutes)',
  },
  auto_rotation_tips: {
    id: `${scope}.auto_rotation_tips`,
    defaultMessage: 'Please enter 0 to turn off rotation. Min auto rotation time is',
  },
  setup_tcp_os_profile: {
    id: `${scope}.setup_tcp_os_profile`,
    defaultMessage: 'Setup TCP/IP OS Profile',
  },
  tcp_os_instruction: {
    id: `${scope}.tcp_os_instruction`,
    defaultMessage: 'System will modify TCP/IP packets to spoofing TCP/IP check. So speed will be decreased. Consider to use it. Linux profile is default option.',
  },
  tcp_os_profile: {
    id: `${scope}.tcp_os_profile`,
    defaultMessage: 'TCP/IP OS Profile',
  },
  tcp_ip_os: {
    id: `${scope}.tcp_ip_os`,
    defaultMessage: 'TCP/IP OS',
  },
  current_location: {
    id: `${scope}.current_location`,
    defaultMessage: 'Current Location',
  },
  select_new_location: {
    id: `${scope}.select_new_location`,
    defaultMessage: 'Select New Location',
  },
  location_change_fee: {
    id: `${scope}.location_change_fee`,
    defaultMessage: 'Location Change Fee',
  },
  location_change_fee_description: {
    id: `${scope}.location_change_fee_description`,
    defaultMessage: 'A one-time fee of {fee} will be charged for changing your proxy location. This change will take effect immediately.',
  },
  change_location_button: {
    id: `${scope}.change_location_button`,
    defaultMessage: 'Change Location {fee}',
  },
  cancel: {
    id: `${scope}.cancel`,
    defaultMessage: 'Cancel',
  },
  location_change_note: {
    id: `${scope}.location_change_note`,
    defaultMessage: 'Location changes are processed immediately. Your proxy configuration will be updated.',
  },
});
